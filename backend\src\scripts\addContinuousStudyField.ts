import { connectDB, sequelize } from '../config/db';
import { QueryInterface, DataTypes } from 'sequelize';

/**
 * 添加连续学习时间字段到StudySession表的迁移脚本
 */
async function addContinuousStudyField() {
  try {
    console.log('🔄 开始添加连续学习时间字段到StudySession表...');
    
    // 连接数据库
    await connectDB();
    
    const queryInterface: QueryInterface = sequelize.getQueryInterface();
    
    // 检查表是否存在
    const tables = await queryInterface.showAllTables();
    const studySessionTableName = 'study_sessions';
    
    if (!tables.includes(studySessionTableName)) {
      console.log(`❌ 表 ${studySessionTableName} 不存在`);
      return;
    }
    
    // 获取表的当前结构
    const tableDescription = await queryInterface.describeTable(studySessionTableName);
    console.log('📋 当前表结构:', Object.keys(tableDescription));
    
    // 检查字段是否已存在
    const fieldName = 'continuousStudyStartTime';
    
    if (tableDescription[fieldName]) {
      console.log(`✅ 字段 ${fieldName} 已存在，跳过添加`);
      return;
    }
    
    // 添加字段
    console.log(`➕ 添加字段: ${fieldName}`);
    await queryInterface.addColumn(studySessionTableName, fieldName, {
      type: DataTypes.DATE,
      allowNull: true,
    });
    
    console.log(`✅ 字段 ${fieldName} 添加成功`);
    
    // 验证字段是否添加成功
    const updatedTableDescription = await queryInterface.describeTable(studySessionTableName);
    if (updatedTableDescription[fieldName]) {
      console.log(`✅ 验证成功: 字段 ${fieldName} 已存在于表中`);
    } else {
      console.log(`❌ 验证失败: 字段 ${fieldName} 未找到`);
    }
    
    console.log('🎉 连续学习时间字段添加完成!');
    
  } catch (error) {
    console.error('❌ 添加连续学习时间字段时发生错误:', error);
    throw error;
  } finally {
    // 关闭数据库连接
    await sequelize.close();
    console.log('🔌 数据库连接已关闭');
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  addContinuousStudyField()
    .then(() => {
      console.log('✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

export { addContinuousStudyField };